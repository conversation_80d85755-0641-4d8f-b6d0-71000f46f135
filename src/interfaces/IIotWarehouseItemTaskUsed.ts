/**
 * Generate by E:\WORK\PYROJECT\VIIS\iot-backend-typescript\tools\gen_type.js
 */
export class IIotWarehouseItemTaskUsed {
  name!: string | number;
  quantity?: number; // Float
  description?: string; // Data
  task_id!: string; // Link
  iot_category_id!: string; // Link
  exp_quantity?: number; // Float
  loss_quantity?: number; // Float
  issued_quantity?: number; // Float
  draft_quantity?: number; // Float
  total_qty_in_crop?: number; // Float
  oldID?: any;
  //UOM related
  active_uom?: string; // Link
  active_conversion_factor?: number; // Float
}
