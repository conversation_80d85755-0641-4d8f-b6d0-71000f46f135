import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON>umn, <PERSON>ToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Un<PERSON> } from 'typeorm';
import { CustomBaseEntity } from '../../base/Base';
import { FarmingPlanTask } from '../FarmingPlanTask';
import { Item } from '../../stock/item/Item';
import { UOM } from '../../UOM/UOM';

@Entity({ name: 'tabiot_production_quantity' })
@Unique(['task_id', 'product_id'])
export class ProductionQuantity extends CustomBaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 140 })
  name: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  quantity: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  uinit?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  packing_unit?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  task_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  unit?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, nullable: true })
  exp_quantity?: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  label?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  product_id?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  lost_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  finished_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  issued_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  total_qty_in_crop: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  draft_quantity: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  active_uom?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  active_conversion_factor: number;

  // Relations
  @ManyToOne(() => FarmingPlanTask, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'task_id', referencedColumnName: 'name' })
  task?: FarmingPlanTask;

  @ManyToOne(() => Item, { nullable: true })
  @JoinColumn({ name: 'product_id', referencedColumnName: 'name' })
  product?: Item;

  @ManyToOne(() => UOM, { nullable: true, onDelete: 'SET NULL', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'active_uom', referencedColumnName: 'name' })
  activeUOM?: UOM;
}
