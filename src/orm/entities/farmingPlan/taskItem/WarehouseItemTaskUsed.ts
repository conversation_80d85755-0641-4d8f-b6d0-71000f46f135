import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON>umn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Unique } from 'typeorm';
import { CustomBaseEntity } from '../../base/Base';
import { FarmingPlanTask } from '../FarmingPlanTask';
import { Item } from '../../stock/item/Item';
import { UOM } from '../../UOM/UOM';

@Entity({ name: 'tabiot_warehouse_item_task_used' })
@Unique(['task_id', 'iot_category_id'])
export class WarehouseItemTaskUsed extends CustomBaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 140 })
  name: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  quantity: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  task_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  iot_category_id?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  exp_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  loss_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  issued_quantity: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  total_qty_in_crop: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  draft_quantity: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  active_uom?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  active_conversion_factor: number;

  // Relations
  @ManyToOne(() => FarmingPlanTask, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'task_id', referencedColumnName: 'name' })
  task?: FarmingPlanTask;

  @ManyToOne(() => Item, { nullable: true })
  @JoinColumn({ name: 'iot_category_id', referencedColumnName: 'name' })
  iotCategory?: Item;

  @ManyToOne(() => UOM, { nullable: true, onDelete: 'SET NULL', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'active_uom', referencedColumnName: 'name' })
  activeUOM?: UOM;
}