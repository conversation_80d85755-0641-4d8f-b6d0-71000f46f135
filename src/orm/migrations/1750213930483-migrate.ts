import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrate1750213930483 implements MigrationInterface {
    name = 'Migrate1750213930483'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tabiot_warehouse_item_task_used" DROP CONSTRAINT "FK_4473d0bfa2c27049ef04d5c9360"`);
        await queryRunner.query(`ALTER TABLE "tabiot_production_quantity" DROP CONSTRAINT "FK_e237e003a0307207dd14b337223"`);
        await queryRunner.query(`ALTER TABLE "tabiot_warehouse_item_task_used" ADD CONSTRAINT "FK_4473d0bfa2c27049ef04d5c9360" FOREIGN KEY ("active_uom") REFERENCES "tabUOM"("name") ON DELETE SET NULL ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "tabiot_production_quantity" ADD CONSTRAINT "FK_e237e003a0307207dd14b337223" FOREIGN KEY ("active_uom") REFERENCES "tabUOM"("name") ON DELETE SET NULL ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tabiot_production_quantity" DROP CONSTRAINT "FK_e237e003a0307207dd14b337223"`);
        await queryRunner.query(`ALTER TABLE "tabiot_warehouse_item_task_used" DROP CONSTRAINT "FK_4473d0bfa2c27049ef04d5c9360"`);
        await queryRunner.query(`ALTER TABLE "tabiot_production_quantity" ADD CONSTRAINT "FK_e237e003a0307207dd14b337223" FOREIGN KEY ("active_uom") REFERENCES "tabUOM"("name") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tabiot_warehouse_item_task_used" ADD CONSTRAINT "FK_4473d0bfa2c27049ef04d5c9360" FOREIGN KEY ("active_uom") REFERENCES "tabUOM"("name") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
