/**
 * UOM (Unit of Measure) Helper Utilities
 * Provides standardized functions for handling active_uom and conversion factors
 */

export interface UOMData {
  active_uom?: string | null;
  active_conversion_factor?: number | null;
}

export interface StandardizedUOMData {
  active_uom: string | null;
  active_conversion_factor: number;
}

/**
 * Default values for UOM operations
 */
export const UOM_DEFAULTS = {
  CONVERSION_FACTOR: 1,
  EMPTY_UOM: null,
  MIN_CONVERSION_FACTOR: 0.000000001,
  MAX_CONVERSION_FACTOR: 999999999,
  MAX_UOM_LENGTH: 140
} as const;

/**
 * Standardize active_uom value
 * @param activeUom - Raw active_uom value
 * @returns Standardized active_uom (null for empty/invalid values)
 */
export function standardizeActiveUOM(activeUom: string | null | undefined): string | null {
  if (!activeUom || typeof activeUom !== 'string') {
    return null;
  }
  
  const trimmed = activeUom.trim();
  if (trimmed === '' || trimmed.length > UOM_DEFAULTS.MAX_UOM_LENGTH) {
    return null;
  }
  
  return trimmed;
}

/**
 * Standardize conversion factor value
 * @param conversionFactor - Raw conversion factor value
 * @returns Standardized conversion factor (default 1 for invalid values)
 */
export function standardizeConversionFactor(conversionFactor: number | null | undefined): number {
  if (conversionFactor === null || conversionFactor === undefined || isNaN(conversionFactor)) {
    return UOM_DEFAULTS.CONVERSION_FACTOR;
  }
  
  if (conversionFactor <= 0 || !isFinite(conversionFactor)) {
    return UOM_DEFAULTS.CONVERSION_FACTOR;
  }
  
  if (conversionFactor < UOM_DEFAULTS.MIN_CONVERSION_FACTOR || conversionFactor > UOM_DEFAULTS.MAX_CONVERSION_FACTOR) {
    return UOM_DEFAULTS.CONVERSION_FACTOR;
  }
  
  return conversionFactor;
}

/**
 * Standardize UOM data object
 * @param data - Object containing active_uom and active_conversion_factor
 * @returns Standardized UOM data
 */
export function standardizeUOMData(data: UOMData): StandardizedUOMData {
  return {
    active_uom: standardizeActiveUOM(data.active_uom),
    active_conversion_factor: standardizeConversionFactor(data.active_conversion_factor)
  };
}

/**
 * Check if UOM data is valid
 * @param data - UOM data to validate
 * @returns True if data is valid
 */
export function isValidUOMData(data: UOMData): boolean {
  const standardized = standardizeUOMData(data);
  
  // If UOM is provided, it must be valid
  if (data.active_uom && !standardized.active_uom) {
    return false;
  }
  
  // Conversion factor must be valid
  if (data.active_conversion_factor !== undefined && 
      standardized.active_conversion_factor !== data.active_conversion_factor) {
    return false;
  }
  
  return true;
}

/**
 * Create UOM data for database insertion
 * @param activeUom - Active UOM value
 * @param conversionFactor - Conversion factor value
 * @returns Object ready for database insertion
 */
export function createUOMDataForDB(
  activeUom?: string | null,
  conversionFactor?: number | null
): { active_uom: string | null; active_conversion_factor: number } {
  const standardized = standardizeUOMData({
    active_uom: activeUom,
    active_conversion_factor: conversionFactor
  });
  
  return {
    active_uom: standardized.active_uom,
    active_conversion_factor: standardized.active_conversion_factor
  };
}

/**
 * Create UOM data for API response
 * @param activeUom - Active UOM value
 * @param conversionFactor - Conversion factor value
 * @returns Object ready for API response
 */
export function createUOMDataForAPI(
  activeUom?: string | null,
  conversionFactor?: number | null
): { active_uom: string | undefined; active_conversion_factor: number } {
  const standardized = standardizeUOMData({
    active_uom: activeUom,
    active_conversion_factor: conversionFactor
  });
  
  return {
    active_uom: standardized.active_uom || undefined,
    active_conversion_factor: standardized.active_conversion_factor
  };
}

/**
 * Merge UOM data with defaults
 * @param existingData - Existing UOM data
 * @param newData - New UOM data to merge
 * @returns Merged and standardized UOM data
 */
export function mergeUOMData(
  existingData: UOMData,
  newData: Partial<UOMData>
): StandardizedUOMData {
  return standardizeUOMData({
    active_uom: newData.active_uom !== undefined ? newData.active_uom : existingData.active_uom,
    active_conversion_factor: newData.active_conversion_factor !== undefined 
      ? newData.active_conversion_factor 
      : existingData.active_conversion_factor
  });
}

/**
 * Format UOM data for logging
 * @param data - UOM data to format
 * @returns Formatted string for logging
 */
export function formatUOMDataForLog(data: UOMData): string {
  const standardized = standardizeUOMData(data);
  return `UOM: ${standardized.active_uom || 'null'}, Factor: ${standardized.active_conversion_factor}`;
}

/**
 * Validate UOM data and return error messages
 * @param data - UOM data to validate
 * @returns Array of error messages (empty if valid)
 */
export function validateUOMDataWithMessages(data: UOMData): string[] {
  const errors: string[] = [];
  
  if (data.active_uom !== undefined && data.active_uom !== null) {
    if (typeof data.active_uom !== 'string') {
      errors.push('Active UOM must be a string');
    } else if (data.active_uom.trim() === '') {
      errors.push('Active UOM cannot be empty');
    } else if (data.active_uom.length > UOM_DEFAULTS.MAX_UOM_LENGTH) {
      errors.push(`Active UOM cannot exceed ${UOM_DEFAULTS.MAX_UOM_LENGTH} characters`);
    }
  }
  
  if (data.active_conversion_factor !== undefined && data.active_conversion_factor !== null) {
    if (typeof data.active_conversion_factor !== 'number' || isNaN(data.active_conversion_factor)) {
      errors.push('Active conversion factor must be a valid number');
    } else if (data.active_conversion_factor <= 0) {
      errors.push('Active conversion factor must be greater than 0');
    } else if (!isFinite(data.active_conversion_factor)) {
      errors.push('Active conversion factor must be a finite number');
    } else if (data.active_conversion_factor < UOM_DEFAULTS.MIN_CONVERSION_FACTOR) {
      errors.push(`Active conversion factor must be at least ${UOM_DEFAULTS.MIN_CONVERSION_FACTOR}`);
    } else if (data.active_conversion_factor > UOM_DEFAULTS.MAX_CONVERSION_FACTOR) {
      errors.push(`Active conversion factor cannot exceed ${UOM_DEFAULTS.MAX_CONVERSION_FACTOR}`);
    }
  }
  
  return errors;
}

/**
 * Check if two UOM data objects are equivalent
 * @param data1 - First UOM data
 * @param data2 - Second UOM data
 * @returns True if equivalent
 */
export function areUOMDataEquivalent(data1: UOMData, data2: UOMData): boolean {
  const std1 = standardizeUOMData(data1);
  const std2 = standardizeUOMData(data2);
  
  return std1.active_uom === std2.active_uom && 
         std1.active_conversion_factor === std2.active_conversion_factor;
}

/**
 * Create safe UOM data for SQL queries (prevents SQL injection)
 * @param activeUom - Active UOM value
 * @param conversionFactor - Conversion factor value
 * @returns Object with escaped values for SQL
 */
export function createSafeUOMDataForSQL(
  activeUom?: string | null,
  conversionFactor?: number | null
): { active_uom: string; active_conversion_factor: number } {
  const standardized = standardizeUOMData({
    active_uom: activeUom,
    active_conversion_factor: conversionFactor
  });
  
  return {
    active_uom: standardized.active_uom || '',
    active_conversion_factor: standardized.active_conversion_factor
  };
}
