import { FrappeService } from '@app/services/frappe/FrappeService';
import Container, { Service } from 'typedi';
import { ICurrentUser } from '@app/interfaces';
import jwt from 'jsonwebtoken';
import configs from '@app/configs';
import axios, { AxiosInstance, CreateAxiosDefaults } from 'axios';
import { HttpError } from 'routing-controllers';
import bodyParser from 'body-parser';
import { UserType } from '@app/utils/enums/usertype';
import { IIotProject } from '@app/interfaces/IIotProject';
import { ERPExecute, ERPExecuteWithTransaction } from '@app/loaders/pglib/PGDB';
import { IIotZone } from '@app/interfaces/IIotZone';
import { IIotDevice } from '@app/interfaces/IIotDevice';
import { IIotProductionFunction } from '@app/interfaces/IIotProductionFunction';
import { IGeneralDoc } from '@app/interfaces/IGeneralDoc';
import { convertObjectArray, getTotalItemQtyInCrop, updateTotalItemQtyInCrop } from './TaskItemValidator';
import { randomUUID, verify } from 'crypto';
import { OtherService } from '@app/services/frappe/OtherService';
import { IIotWarehouseItemTaskUsed } from '@app/interfaces/IIotWarehouseItemTaskUsed';
import { SqlQuery } from '@app/interfaces/ISQLQuery';
import e from 'express';
import moment from 'moment';
import { StockEntryService } from '@app/modules/stock-v3/stock-entry/StockEntryService';
import { CropService } from '@app/modules/crop-manage/crop/CropService';
import { IMaterialTotal } from '@app/modules/stock-v3/stock-entry/StockEntryValidator';
import { parseFilterParams } from '@app/utils/helpers/helper';

@Service()
export class TaskItemService {
  constructor(
    private frappeService: FrappeService,
    private otherService: OtherService,
    private stockEntryService: StockEntryService,
    private cropService: CropService,
  ) {
    this.frappeService = Container.get(FrappeService);
    this.otherService = Container.get(OtherService);
    this.stockEntryService = Container.get(StockEntryService);
    this.cropService = Container.get(CropService);
  }

  // async getTaskItem(user: ICurrentUser , params: any) {
  //   try {
  //     if (user.user_type === UserType.SYSTEM_USER) {
  //       // const response = await this.frappeService.callSScript("get-list-location", param, "GET")
  //       const taskItemList: any = await this.frappeService.callSScript(
  //         'general-doc-list',
  //         {
  //           filters: JSON.stringify(params.filters) || JSON.stringify([]),
  //           or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
  //           page: params.page || 1,
  //           size: params.size || 10000,
  //           fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
  //           order_by: params.order_by || null,
  //           group_by: params.group_by || null,
  //           doc_name: 'iot_taskItem',
  //         },
  //         'GET',
  //       );
  //       return taskItemList;
  //     } else {
  //       /**
  //        * verify user
  //        */
  //       const taskItemList = await getTaskItemList(user);

  //       if (taskItemList.length === 0) {
  //         return {
  //           data: [],
  //           pagination: {
  //             pageNumber: 1,
  //             pageSize: 100,
  //             totalElements: 0,
  //             totalPages: 0,
  //
  //           },
  //         };
  //       }

  //       let conditionArr: any[] = [];
  //       taskItemList.forEach((d: any) => {
  //         conditionArr.push(d.name);
  //       });
  //       const filters: any = [['iot_taskItem', 'name', 'in', conditionArr]];
  //       if (params.filters) {
  //         const paramsFilterArr: any[] = JSON.parse(params.filters);
  //         paramsFilterArr.forEach((d: any) => {
  //           filters.push(d);
  //         });
  //       }
  //       let taskItems: any = await this.frappeService.callSScript(
  //         'general-doc-list',
  //         {
  //           filters: JSON.stringify(filters) || JSON.stringify([]),
  //           or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
  //           page: params.page || 1,
  //           size: params.size || 10000,
  //           fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
  //           order_by: params.order_by || null,
  //           group_by: params.group_by || null,
  //           doc_name: 'iot_taskItem',
  //         },
  //         'GET',
  //       );
  //       return taskItems;
  //     }
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async getAllTaskItemUsed(user: ICurrentUser, params: IGeneralDoc) {
    try {
      let filters: any[] = [];
      if (params.filters) {
        let paramsFilters = JSON.parse(params.filters);
        paramsFilters.forEach((element: any) => {
          filters.push(element);
        });
      }

      let page = params.page ? parseInt(params.page) : 1;
      let size = params.size ? parseInt(params.size) : 10000;
      let sqlConditionStr = parseFilterParams(filters);

      const query: string = `
        SELECT
          "tabiot_warehouse_item_task_used".name,
          "tabiot_warehouse_item_task_used".description,
          "tabiot_warehouse_item_task_used".task_id,
          "tabiot_warehouse_item_task_used".iot_category_id,
          "tabiot_warehouse_item_task_used".exp_quantity,
          "tabiot_warehouse_item_task_used".quantity,
          "tabiot_warehouse_item_task_used".loss_quantity,
          "tabiot_warehouse_item_task_used".issued_quantity,
          "tabiot_warehouse_item_task_used".total_qty_in_crop,
          "tabiot_warehouse_item_task_used".creation,
          "tabiot_warehouse_item_task_used".modified,
          "tabiot_warehouse_item_task_used".draft_quantity,
          "tabItem".label AS item_label,
          "tabUOM".name AS uom_id,
          "tabUOM".uom_name AS uom_label,
          1 AS conversion_factor,
          1 AS ratio,
          (
            SELECT json_agg(
              json_build_object(
                'uom_id', "tabUOM Conversion Detail".uom,
                'uom_label', uom_table.uom_name,
                'conversion_factor', "tabUOM Conversion Detail".conversion_factor
              )
              ORDER BY "tabUOM Conversion Detail".conversion_factor ASC
            )
            FROM "tabUOM Conversion Detail"
            LEFT JOIN "tabUOM" AS uom_table ON "tabUOM Conversion Detail".uom = uom_table.name
            WHERE "tabUOM Conversion Detail".parent = "tabiot_warehouse_item_task_used".iot_category_id
          ) AS uoms
        FROM
          "tabiot_warehouse_item_task_used"
        LEFT JOIN
          "tabItem" ON "tabItem".name = "tabiot_warehouse_item_task_used".iot_category_id
        LEFT JOIN
          "tabUOM" ON "tabUOM".name = "tabItem".stock_uom
        WHERE TRUE
          ${sqlConditionStr}
        ORDER BY "tabItem".label ASC
        LIMIT ${size} OFFSET ${(page - 1) * size}        
      `;

      const taskItemList: any = await ERPExecute(query, []);
      const queryCount: string = `
        SELECT
          COUNT("tabiot_warehouse_item_task_used".name) AS total
        FROM
          "tabiot_warehouse_item_task_used"
        LEFT JOIN
          "tabItem" ON "tabItem".name = "tabiot_warehouse_item_task_used".iot_category_id
        LEFT JOIN
          "tabUOM" ON "tabUOM".name = "tabItem".stock_uom
        WHERE TRUE
          ${sqlConditionStr}
      `;
      const total: any = await ERPExecute(queryCount, []);
      return {
        data: taskItemList,
        pagination: {
          pageNumber: page,
          pageSize: size,
          totalElements: total[0].total,
          totalPages: Math.ceil(total[0].total / size),
        },
      };

    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }


  async getTotalItemQtyInCrop(params: { user: ICurrentUser; item_id: string; crop_id: string }) {
    try {
      const paramsMaterialTotal: any = {
        item_code: params.item_id,
        iot_crop: params.crop_id,
      } as IMaterialTotal;
      const totalQtyInCropList = await this.stockEntryService.getMaterialTotalBalanceForItem(
        params.user,
        paramsMaterialTotal,
      );
      return totalQtyInCropList;
    } catch (error) {
      throw error;
    }
  }

  async createTaskItemAdmin(user: ICurrentUser, body: IIotWarehouseItemTaskUsed[]) {
    // const response = await this.frappeService.generalCreate({
    //   doc_name: 'iot_warehouse_item_task_used',
    //   data: body,
    // });
    try {
      const sqlArray: SqlQuery[] = [];
      for (const item of body) {
        const name = randomUUID();
        const creation = moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss');
        //Calculate total_qty_in_crop for the current item and crop
        // const totalQtyInCrop = await getTotalItemQtyInCrop({ item_id: item.iot_category_id, task_id: item.task_id });

        const crop = await this.cropService.getCropByTask(user, { task_id: item.task_id });
        const totalQtyInCrop = await this.getTotalItemQtyInCrop({
          user,
          item_id: item.iot_category_id,
          crop_id: crop.name,
        });
        const findTotalQtyInCrop: any = totalQtyInCrop.find(
          (totalQty: any) => totalQty.item_id === item.iot_category_id,
        );
        const sqlQuery: SqlQuery = {
          query: `
          INSERT INTO tabiot_warehouse_item_task_used
          (name, quantity, description, task_id, iot_category_id, exp_quantity,
          loss_quantity, issued_quantity, total_qty_in_crop, active_uom,
          active_conversion_factor, creation, modified)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $12)
          RETURNING *;
          `,
          params: [
            name,
            item.quantity,
            item.description,
            item.task_id,
            item.iot_category_id,
            item.exp_quantity,
            item.loss_quantity,
            item.issued_quantity,
            findTotalQtyInCrop ? findTotalQtyInCrop.total_qty : 0,
            item.active_uom || null,
            item.active_conversion_factor || 1,
            creation,
          ],
        };
        sqlArray.push(sqlQuery);
      }
      const response = await ERPExecuteWithTransaction(sqlArray);

      //update total_qty_in_crop for all tabiot_warehouse_item_task_used with the given iot_category_id
      for (const item of body) {
        await this.updateTotalItemQtyInCrop(user, { task_id: item.task_id, item_id: item.iot_category_id });
      }
      return response;
    } catch (error) {
      throw error;
    }
  }

  async updateTaskItemAdmin(user: ICurrentUser, body: IIotWarehouseItemTaskUsed[]) {
    try {
      console.log('body', body);
      const sqlArrayUpdateQuantity: SqlQuery[] = [];
      const sqlArrayUpdateTotalQty: SqlQuery[] = [];

      for (const item of body) {
        // Build dynamic query based on non-null fields
        const fields: string[] = [];
        const params: any[] = [item.name];
        let paramIndex = 2; // Start from 2 because 1 is reserved for the item name

        if (item.quantity !== undefined) {
          fields.push(`quantity = $${paramIndex}`);
          params.push(item.quantity);
          paramIndex++;
        }
        if (item.description !== undefined) {
          fields.push(`description = $${paramIndex}`);
          params.push(item.description);
          paramIndex++;
        }
        if (item.task_id !== undefined) {
          fields.push(`task_id = $${paramIndex}`);
          params.push(item.task_id);
          paramIndex++;
        }
        if (item.iot_category_id !== undefined) {
          fields.push(`iot_category_id = $${paramIndex}`);
          params.push(item.iot_category_id);
          paramIndex++;
        }
        if (item.exp_quantity !== undefined) {
          fields.push(`exp_quantity = $${paramIndex}`);
          params.push(item.exp_quantity);
          paramIndex++;
        }
        if (item.loss_quantity !== undefined) {
          fields.push(`loss_quantity = $${paramIndex}`);
          params.push(item.loss_quantity);
          paramIndex++;
        }
        if (item.issued_quantity !== undefined) {
          fields.push(`issued_quantity = $${paramIndex}`);
          params.push(item.issued_quantity);
          paramIndex++;
        }
        if (item.draft_quantity !== undefined) {
          fields.push(`draft_quantity = $${paramIndex}`);
          params.push(item.draft_quantity);
          paramIndex++;
        }
        // UOM related fields
        if (item.active_uom !== undefined) {
          fields.push(`active_uom = $${paramIndex}`);
          params.push(item.active_uom);
          paramIndex++;
        }
        if (item.active_conversion_factor !== undefined) {
          fields.push(`active_conversion_factor = $${paramIndex}`);
          params.push(item.active_conversion_factor);
          paramIndex++;
        }

        // Always update modified timestamp
        fields.push(`modified = $${paramIndex}`);
        params.push(moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'));

        const sqlQueryUpdateQuantity: SqlQuery = {
          query: `
                UPDATE tabiot_warehouse_item_task_used
                SET ${fields.join(', ')}
                WHERE name = $1;
                `,
          params: params,
        };
        sqlArrayUpdateQuantity.push(sqlQueryUpdateQuantity);
      }

      // Execute the first batch of queries
      await ERPExecuteWithTransaction(sqlArrayUpdateQuantity);

      for (const item of body) {
        // Then, calculate total_qty_in_crop for the current item and crop
        const crop = await this.cropService.getCropByTask(user, { task_id: item.task_id });
        const totalQtyInCrop = await this.getTotalItemQtyInCrop({
          user,
          item_id: item.iot_category_id,
          crop_id: crop.name,
        });
        console.log({ totalQtyInCrop_in_item: totalQtyInCrop });

        // Find totalQtyInCrop for the current item and crop
        const findTotalQtyInCrop: any = totalQtyInCrop.find(
          (totalQty: any) => totalQty.item_id === item.iot_category_id,
        );

        // Finally, update total_qty_in_crop
        const sqlQueryUpdateTotalQty: SqlQuery = {
          query: `
                UPDATE tabiot_warehouse_item_task_used
                SET total_qty_in_crop = $2
                WHERE name = $1;
                `,
          params: [item.name, findTotalQtyInCrop ? findTotalQtyInCrop.total_qty : 0],
        };
        sqlArrayUpdateTotalQty.push(sqlQueryUpdateTotalQty);
      }
      console.log('sqlQueryUpdateTotalQty', sqlArrayUpdateTotalQty);
      // Execute the second batch of queries
      const response = await ERPExecuteWithTransaction(sqlArrayUpdateTotalQty);
      // Update total_qty_in_crop for all tabiot_warehouse_item_task_used with the given iot_category_id
      for (const item of body) {
        await this.updateTotalItemQtyInCrop(user, { task_id: item.task_id, item_id: item.iot_category_id });
      }
      return response;
    } catch (error) {
      throw error;
    }
  }

  async deleteTaskItemAdmin(user: ICurrentUser, params: { name: string }) {
    const deleteRc: any = await this.frappeService.generalDelete({
      name: params.name,
      doc_name: 'iot_warehouse_item_task_used',
    });
    return deleteRc;
  }

  async updateTotalItemQtyInCrop(user: ICurrentUser, params: { task_id: string; item_id: string }) {
    try {
      //get crop id from task id
      const crop = await this.cropService.getCropByTask(user, { task_id: params.task_id });
      // const totalQtyInCrop = await getTotalItemQtyInCrop({ item_id: params.item_id, task_id: params.task_id });
      const totalQtyInCrop = await this.getTotalItemQtyInCrop({
        user,
        item_id: params.item_id,
        crop_id: crop.name,
      });

      const taskList = await ERPExecute(
        `
        WITH temp_table AS (
        SELECT
            crop.name AS crop_id,
            plan.name AS plan_id
        FROM tabiot_crop AS crop
        INNER JOIN
          tabiot_farming_plan AS plan
          ON plan.crop = crop.name
        INNER JOIN
          tabiot_farming_plan_state AS state
          ON state.farming_plan = plan.name
        INNER JOIN
          tabiot_farming_plan_task AS task
          ON task.farming_plan_state = state.name
        INNER JOIN
          tabiot_warehouse_item_task_used AS item_task
          ON item_task.task_id = task.name
        WHERE
          task.name = $1)
        SELECT
          task.name AS task_id
        FROM
          tabiot_farming_plan_task AS task
        INNER JOIN
          tabiot_farming_plan_state AS state
          ON task.farming_plan_state = state.name
        INNER JOIN
          temp_table
          ON state.farming_plan = temp_table.plan_id
        `,
        [params.task_id],
      );
      const taskListSQL = taskList.map((task: any) => `'${task.task_id}'`).join(','); // Then, update total_qty_in_crop for all tabiot_warehouse_item_task_used with the given iot_category_id
      const sqlQuery: SqlQuery = {
        query: `
              UPDATE tabiot_warehouse_item_task_used
              SET total_qty_in_crop = $2
              WHERE iot_category_id = $1 AND task_id IN (${taskListSQL});
              `,
        params: [params.item_id, totalQtyInCrop.length ? totalQtyInCrop[0].total_qty : 0],
      };

      // Execute the query
      const response = await ERPExecuteWithTransaction([sqlQuery]);

      return response;
    } catch (error) {
      throw error;
    }
  }
}
